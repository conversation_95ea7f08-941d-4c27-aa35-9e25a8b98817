import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, RotateCcw, Trophy, Star, Clock, Shuffle } from 'lucide-react'
import {
  getImageCollection,
  getGameProgress,
  updateGameProgress,
  unlockAchievement
} from '../../utils/localStorage'
import { playClickSound, playSuccessSound } from '../../utils/audio'
import { useLanguage } from '../../contexts/LanguageContext'

const PuzzleGame = ({ childName }) => {
  const [gameState, setGameState] = useState('menu') // menu, playing, completed
  const [selectedImage, setSelectedImage] = useState(null)
  const [puzzlePieces, setPuzzlePieces] = useState([])
  const [placedPieces, setPlacedPieces] = useState({})
  const [draggedPiece, setDraggedPiece] = useState(null)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [gameStartTime, setGameStartTime] = useState(null)
  const [difficulty, setDifficulty] = useState('easy') // easy: 3x3, medium: 4x4, hard: 5x5
  const [availableImages, setAvailableImages] = useState([])
  const [gameProgress, setGameProgress] = useState({})
  const [score, setScore] = useState(0)
  const { t } = useLanguage()
  const navigate = useNavigate()

  // Difficulty settings
  const difficultySettings = {
    easy: { gridSize: 3, timeBonus: 200 },
    medium: { gridSize: 4, timeBonus: 300 },
    hard: { gridSize: 5, timeBonus: 500 }
  }

  useEffect(() => {
    if (childName) {
      // Load available images
      const collection = getImageCollection(childName)
      const allImages = collection.themes?.reduce((acc, theme) => {
        return [...acc, ...theme.images]
      }, []) || []
      setAvailableImages(allImages)

      // Load game progress
      const progress = getGameProgress(childName)
      setGameProgress(progress.puzzle || { completed: 0, favoriteImage: null, bestTime: null })
    }
  }, [childName])

  // Timer effect
  useEffect(() => {
    let interval = null
    if (gameState === 'playing' && gameStartTime) {
      interval = setInterval(() => {
        setTimeElapsed(Math.floor((Date.now() - gameStartTime) / 1000))
      }, 1000)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [gameState, gameStartTime])

  // Check for completion
  useEffect(() => {
    if (gameState !== 'playing' || puzzlePieces.length === 0) return

    const totalPieces = difficultySettings[difficulty].gridSize ** 2
    const correctlyPlacedPieces = Object.entries(placedPieces).filter(([_, pos]) => pos.isInCorrectPosition)

    if (correctlyPlacedPieces.length === totalPieces) {
      // All pieces are correctly placed
      setTimeout(() => {
        setGameState('completed')
        playSuccessSound()

        const finalTime = Math.floor((Date.now() - gameStartTime) / 1000)
        setTimeElapsed(finalTime)

        const finalScore = Math.max(difficultySettings[difficulty].timeBonus - finalTime, 50)
        setScore(finalScore)

        // Update progress
        const newProgress = {
          ...gameProgress,
          completed: (gameProgress.completed || 0) + 1,
          bestTime: gameProgress.bestTime ? Math.min(gameProgress.bestTime, finalTime) : finalTime,
          favoriteImage: selectedImage?.id
        }

        updateGameProgress(childName, 'puzzle', newProgress)
        setGameProgress(newProgress)

        // Check for achievements
        if (newProgress.completed >= 3) {
          unlockAchievement(childName, 'puzzleMaster')
        }
      }, 500)
    }
  }, [placedPieces, gameState, puzzlePieces.length, difficulty, gameStartTime, gameProgress, selectedImage, childName])

  const generateJigsawShape = (row, col, gridSize, allShapes = {}) => {
    // Use deterministic random based on position for consistent shapes
    const seed = row * gridSize + col
    const random = (offset = 0) => {
      const x = Math.sin(seed + offset) * 10000
      return x - Math.floor(x)
    }

    // Check neighboring pieces to ensure complementary shapes
    const topNeighbor = allShapes[`${row-1}-${col}`]
    const leftNeighbor = allShapes[`${row}-${col-1}`]

    // Generate tabs/blanks for each side, ensuring they complement neighbors
    // If neighbor exists, use opposite of their connecting side
    const hasTopTab = row > 0 ? (topNeighbor ? !topNeighbor.hasBottomTab : random(1) > 0.5) : false
    const hasRightTab = col < gridSize - 1 ? random(2) > 0.5 : false
    const hasBottomTab = row < gridSize - 1 ? random(3) > 0.5 : false
    const hasLeftTab = col > 0 ? (leftNeighbor ? !leftNeighbor.hasRightTab : random(4) > 0.5) : false

    // Create SVG path for jigsaw piece with proper interlocking shapes
    const path = `
      M 15,15
      ${hasTopTab ?
        `L 35,15 Q 40,5 50,5 Q 60,5 65,15 L 85,15` :
        `L 85,15`}
      ${hasRightTab ?
        `L 85,35 Q 95,40 95,50 Q 95,60 85,65 L 85,85` :
        `L 85,85`}
      ${hasBottomTab ?
        `L 65,85 Q 60,95 50,95 Q 40,95 35,85 L 15,85` :
        `L 15,85`}
      ${hasLeftTab ?
        `L 15,65 Q 5,60 5,50 Q 5,40 15,35 L 15,15` :
        `L 15,15`}
      Z
    `

    return {
      path,
      hasTopTab,
      hasRightTab,
      hasBottomTab,
      hasLeftTab
    }
  }

  const createPuzzlePieces = (image) => {
    const gridSize = difficultySettings[difficulty].gridSize
    const pieces = []
    const allShapes = {}

    // First pass: generate all shapes ensuring they complement each other
    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        const jigsawShape = generateJigsawShape(row, col, gridSize, allShapes)
        allShapes[`${row}-${col}`] = jigsawShape
      }
    }

    // Second pass: create pieces with the generated shapes
    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        pieces.push({
          id: `${row}-${col}`,
          correctRow: row,
          correctCol: col,
          image: image,
          jigsawShape: allShapes[`${row}-${col}`]
        })
      }
    }

    return pieces.sort(() => Math.random() - 0.5) // Shuffle pieces
  }

  const startGame = (image) => {
    playClickSound()
    setSelectedImage(image)
    const pieces = createPuzzlePieces(image)
    setPuzzlePieces(pieces)
    setPlacedPieces({})
    setTimeElapsed(0)
    setScore(0)
    setGameStartTime(Date.now())
    setGameState('playing')
  }

  const handleDragStart = (e, piece) => {
    setDraggedPiece(piece)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleWorkspaceDrop = (e) => {
    e.preventDefault()

    if (draggedPiece) {
      playClickSound()

      // Get drop position relative to workspace
      const rect = e.currentTarget.getBoundingClientRect()
      const x = e.clientX - rect.left - 40 // Center the piece
      const y = e.clientY - rect.top - 40

      // Remove piece from any previous position
      const newPlacedPieces = { ...placedPieces }

      // Place piece at workspace coordinates
      newPlacedPieces[draggedPiece.id] = {
        x: Math.max(0, Math.min(x, rect.width - 80)),
        y: Math.max(0, Math.min(y, rect.height - 80)),
        isInCorrectPosition: false
      }

      setPlacedPieces(newPlacedPieces)
      setDraggedPiece(null)
    }
  }

  const handleCorrectPositionDrop = (e, targetRow, targetCol) => {
    e.preventDefault()
    e.stopPropagation()

    if (draggedPiece) {
      playClickSound()

      // Check if this is the correct position for this piece
      const isCorrectPosition = draggedPiece.correctRow === targetRow && draggedPiece.correctCol === targetCol

      if (isCorrectPosition) {
        // Remove piece from any previous position
        const newPlacedPieces = { ...placedPieces }

        // Place piece in correct position
        newPlacedPieces[draggedPiece.id] = {
          row: targetRow,
          col: targetCol,
          isInCorrectPosition: true
        }

        setPlacedPieces(newPlacedPieces)
      } else {
        // Wrong position - place in workspace instead
        handleWorkspaceDrop(e)
      }

      setDraggedPiece(null)
    }
  }

  const handleReturnToPieces = (e) => {
    e.preventDefault()

    if (draggedPiece) {
      playClickSound()

      // Remove piece from placed pieces (return to pieces panel)
      const newPlacedPieces = { ...placedPieces }
      delete newPlacedPieces[draggedPiece.id]

      setPlacedPieces(newPlacedPieces)
      setDraggedPiece(null)
    }
  }



  const resetGame = () => {
    playClickSound()
    setGameState('menu')
    setSelectedImage(null)
    setPuzzlePieces([])
    setPlacedPieces({})
    setTimeElapsed(0)
    setScore(0)
    setGameStartTime(null)
  }

  const shufflePieces = () => {
    playClickSound()
    setPlacedPieces({})
    setPuzzlePieces(prev => [...prev].sort(() => Math.random() - 0.5))
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (gameState === 'menu') {
    return (
      <div
        className="min-h-screen p-4 relative"
        style={{
          backgroundImage: 'url(/images/background-game.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {/* Background Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"></div>

        <div className="max-w-6xl mx-auto relative z-10">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <button
              onClick={() => {
                playClickSound()
                navigate('/dashboard')
              }}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <ArrowLeft className="w-6 h-6" />
              <span className="font-bold">{t('backToDashboard')}</span>
            </button>

            <div className="text-center">
              <h1 className="text-3xl font-bold text-white mb-2">🧩 {t('puzzleGame')}</h1>
              <p className="text-white/80">{t('puzzleDesc')}</p>
            </div>

            <div className="w-32"></div>
          </div>

          {/* Game Stats */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8">
            <div className="grid grid-cols-3 gap-6 text-center text-white">
              <div>
                <div className="text-2xl font-bold">{gameProgress.completed || 0}</div>
                <div className="text-sm opacity-80">{t('puzzlesCompleted')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {gameProgress.bestTime ? formatTime(gameProgress.bestTime) : '--:--'}
                </div>
                <div className="text-sm opacity-80">{t('bestTime')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{availableImages.length}</div>
                <div className="text-sm opacity-80">{t('availableImages')}</div>
              </div>
            </div>
          </div>

          {/* Difficulty Selection */}
          <div className="bg-white rounded-3xl p-8 shadow-2xl mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{t('chooseDifficulty')}</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Object.entries(difficultySettings).map(([level, settings]) => (
                <button
                  key={level}
                  onClick={() => {
                    playClickSound()
                    setDifficulty(level)
                  }}
                  className={`p-6 rounded-2xl border-2 transition-all duration-200 ${
                    difficulty === level
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">
                      {level === 'easy' ? '😊' : level === 'medium' ? '🤔' : '😤'}
                    </div>
                    <div className="font-bold text-gray-800 capitalize mb-2">{t(level)}</div>
                    <div className="text-sm text-gray-600">
                      {settings.gridSize}×{settings.gridSize} {t('pieces')}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Image Selection */}
          <div className="bg-white rounded-3xl p-8 shadow-2xl">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{t('chooseImage')}</h2>

            {availableImages.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <div className="text-4xl mb-4">📸</div>
                <p>{t('noImagesAvailable')}</p>
                <p className="text-sm mt-2">{t('generateSomeImages')}</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {availableImages.map((image) => (
                  <button
                    key={image.id}
                    onClick={() => startGame(image)}
                    className="group relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                  >
                    <img
                      src={image.url || image.dataUrl}
                      alt={image.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                      <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity">
                        <span className="text-2xl">🧩</span>
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                      <p className="text-white text-xs font-bold truncate">{image.name}</p>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  const gridSize = difficultySettings[difficulty].gridSize

  return (
    <>
      {/* Add shake animation styles */}
      <style jsx>{`
        @keyframes shake {
          0%, 100% { transform: translateX(0); }
          10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
          20%, 40%, 60%, 80% { transform: translateX(2px); }
        }
        .shake {
          animation: shake 0.6s ease-in-out;
        }
      `}</style>
    <div
      className="min-h-screen p-4 relative"
      style={{
        backgroundImage: 'url(/images/background-game.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Background Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"></div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Game Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => {
              playClickSound()
              setGameState('menu')
            }}
            className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
          >
            <ArrowLeft className="w-6 h-6" />
            <span className="font-bold">{t('backToMenu')}</span>
          </button>

          {/* Game Stats */}
          <div className="flex items-center gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3">
            <div className="flex items-center gap-2 text-white">
              <Clock className="w-5 h-5" />
              <span className="font-bold">{formatTime(timeElapsed)}</span>
            </div>
            <div className="flex items-center gap-2 text-white">
              <Trophy className="w-5 h-5" />
              <span className="font-bold">{Object.keys(placedPieces).length}/{gridSize * gridSize}</span>
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={shufflePieces}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <Shuffle className="w-6 h-6" />
              <span className="font-bold">{t('shuffle')}</span>
            </button>
            <button
              onClick={resetGame}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <RotateCcw className="w-6 h-6" />
              <span className="font-bold">{t('restart')}</span>
            </button>
          </div>
        </div>

        {/* New Layout - Free Workspace + Pieces Panel */}
        <div className="flex gap-6 max-w-7xl mx-auto h-[calc(100vh-200px)]">
          {/* Left Side - Free Workspace Area */}
          <div className="flex-1 bg-white/10 backdrop-blur-sm rounded-3xl p-6 relative">
            <h3 className="text-xl font-bold text-white mb-4">{t('workspace')}</h3>

            {/* Free workspace area where pieces can be placed anywhere */}
            <div
              className="relative w-full h-full bg-white/5 rounded-2xl border-2 border-dashed border-white/30 overflow-hidden"
              onDragOver={handleDragOver}
              onDrop={(e) => handleWorkspaceDrop(e)}
            >
              {/* Show reference image faintly in background */}
              <div className="absolute inset-4 opacity-20 rounded-xl overflow-hidden z-0">
                <img
                  src={selectedImage.url || selectedImage.dataUrl}
                  alt="Reference"
                  className="w-full h-full object-contain"
                />
              </div>

              {/* Correct position grid overlay (subtle) - Lower z-index */}
              <div
                className="absolute inset-4 grid gap-1 opacity-30 z-10"
                style={{
                  gridTemplateColumns: `repeat(${gridSize}, 1fr)`,
                  aspectRatio: '1'
                }}
              >
                {Array.from({ length: gridSize * gridSize }).map((_, index) => {
                  const row = Math.floor(index / gridSize)
                  const col = index % gridSize
                  const hasCorrectPiece = Object.entries(placedPieces).some(([pieceId, pos]) => {
                    const piece = puzzlePieces.find(p => p.id === pieceId)
                    return piece && piece.correctRow === row && piece.correctCol === col && pos.isInCorrectPosition
                  })

                  return (
                    <div
                      key={index}
                      className={`border border-white/20 rounded relative ${hasCorrectPiece ? 'bg-green-500/30' : ''}`}
                      onDragOver={handleDragOver}
                      onDrop={(e) => handleCorrectPositionDrop(e, row, col)}
                    >
                      {hasCorrectPiece && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          {(() => {
                            const correctPieceEntry = Object.entries(placedPieces).find(([pieceId, pos]) => {
                              const piece = puzzlePieces.find(p => p.id === pieceId)
                              return piece && piece.correctRow === row && piece.correctCol === col && pos.isInCorrectPosition
                            })

                            if (!correctPieceEntry) return null

                            const piece = puzzlePieces.find(p => p.id === correctPieceEntry[0])
                            if (!piece) return null

                            return (
                              <svg
                                width="100%"
                                height="100%"
                                viewBox="0 0 100 100"
                                className="drop-shadow-lg"
                              >
                                <defs>
                                  <pattern
                                    id={`correct-pattern-${row}-${col}`}
                                    patternUnits="userSpaceOnUse"
                                    width="100"
                                    height="100"
                                  >
                                    <image
                                      href={selectedImage.url || selectedImage.dataUrl}
                                      x={col * -100}
                                      y={row * -100}
                                      width={gridSize * 100}
                                      height={gridSize * 100}
                                      preserveAspectRatio="xMidYMid slice"
                                    />
                                  </pattern>
                                </defs>
                                <path
                                  d={piece.jigsawShape.path}
                                  fill={`url(#correct-pattern-${row}-${col})`}
                                  stroke="#22c55e"
                                  strokeWidth="2"
                                />
                              </svg>
                            )
                          })()}
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>

              {/* Render pieces placed in workspace - Higher z-index */}
              {Object.entries(placedPieces).map(([pieceId, position]) => {
                const piece = puzzlePieces.find(p => p.id === pieceId)
                if (!piece || position.isInCorrectPosition) return null

                // Only render if piece has x,y coordinates (workspace position)
                if (typeof position.x !== 'number' || typeof position.y !== 'number') return null

                return (
                  <div
                    key={pieceId}
                    className="absolute cursor-move hover:scale-105 transition-transform z-20"
                    style={{
                      left: `${position.x}px`,
                      top: `${position.y}px`,
                      width: '80px',
                      height: '80px'
                    }}
                    draggable
                    onDragStart={(e) => {
                      e.stopPropagation()
                      handleDragStart(e, piece)
                    }}
                    onMouseDown={(e) => e.stopPropagation()}
                  >
                    <svg
                      width="100%"
                      height="100%"
                      viewBox="0 0 100 100"
                      className="drop-shadow-lg pointer-events-none"
                    >
                      <defs>
                        <pattern
                          id={`workspace-pattern-${pieceId}`}
                          patternUnits="userSpaceOnUse"
                          width="100"
                          height="100"
                        >
                          <image
                            href={selectedImage.url || selectedImage.dataUrl}
                            x={piece.correctCol * -100}
                            y={piece.correctRow * -100}
                            width={gridSize * 100}
                            height={gridSize * 100}
                            preserveAspectRatio="xMidYMid slice"
                          />
                        </pattern>
                      </defs>
                      <path
                        d={piece.jigsawShape.path}
                        fill={`url(#workspace-pattern-${pieceId})`}
                        stroke="#fff"
                        strokeWidth="2"
                        className="hover:stroke-blue-400"
                      />
                    </svg>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Right Side - Puzzle Pieces */}
          <div
            className="w-80 bg-white/10 backdrop-blur-sm rounded-3xl p-6"
            onDragOver={handleDragOver}
            onDrop={handleReturnToPieces}
          >
            <h3 className="text-xl font-bold text-white mb-4 text-center">{t('puzzlePieces')}</h3>
            <div className="grid grid-cols-3 gap-3 max-h-full overflow-y-auto">
              {puzzlePieces.map((piece) => {
                // Only show pieces that are NOT placed anywhere
                const isPlaced = placedPieces[piece.id]
                if (isPlaced) return null

                return (
                  <div
                    key={piece.id}
                    draggable
                    onDragStart={(e) => handleDragStart(e, piece)}
                    className="aspect-square cursor-move hover:shadow-xl transition-all duration-200 transform hover:scale-105 relative"
                    style={{ width: '70px', height: '70px' }}
                  >
                    <svg
                      width="100%"
                      height="100%"
                      viewBox="0 0 100 100"
                      className="drop-shadow-lg"
                    >
                      <defs>
                        <pattern
                          id={`pattern-${piece.id}`}
                          patternUnits="userSpaceOnUse"
                          width="100"
                          height="100"
                        >
                          <image
                            href={selectedImage.url || selectedImage.dataUrl}
                            x={piece.correctCol * -100}
                            y={piece.correctRow * -100}
                            width={gridSize * 100}
                            height={gridSize * 100}
                            preserveAspectRatio="xMidYMid slice"
                          />
                        </pattern>
                      </defs>
                      <path
                        d={piece.jigsawShape.path}
                        fill={`url(#pattern-${piece.id})`}
                        stroke="#fff"
                        strokeWidth="1"
                        className="hover:stroke-blue-400"
                      />
                    </svg>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Game Completed Modal */}
        {gameState === 'completed' && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('puzzleCompleted')}</h2>
              <p className="text-gray-600 mb-6">{t('greatJob')}</p>

              <div className="bg-gray-50 rounded-2xl p-6 mb-6">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{formatTime(timeElapsed)}</div>
                    <div className="text-sm text-gray-600">{t('completionTime')}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{score}</div>
                    <div className="text-sm text-gray-600">{t('score')}</div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setGameState('menu')}
                  className="flex-1 bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200"
                >
                  {t('playAgain')}
                </button>
                <button
                  onClick={() => {
                    playClickSound()
                    navigate('/dashboard')
                  }}
                  className="flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors"
                >
                  {t('backToDashboard')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
    </>
  )
}

export default PuzzleGame
